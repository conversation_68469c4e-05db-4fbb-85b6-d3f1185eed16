# 队列警告功能说明

## 功能概述

在核对结果显示区域下方新增了一个黄色警告区域，用于提示叫号系统队列相关的问题。

## 显示效果

### 警告区域样式
- **背景色**：浅黄色 (#FFF3CD)
- **边框色**：金黄色 (#FFEAA7)
- **文字色**：深棕色 (#856404)
- **提示文字**：叫号系呼叫队列未找到该患者，请注意核对。
- **无图标**：简洁的纯文字提示

### 布局位置
- 位于核对结果显示区域 (VerificationResultBorder) 下方
- 与核对结果区域间距 10px
- 圆角边框，内边距 15px(左右) 12px(上下)
- 文字居中显示，支持自动换行

## 显示时机

### 触发条件
警告区域在以下情况下显示：

1. **配置值为 "1"**（启用队列验证）
2. **患者编号匹配**（scannedId == _currentPatientInfo.PatientId）
3. **队列验证失败**，包括：
   - `queueInfo == null`（队列中未找到该患者）
   - 队列信息对比失败（姓名或性别不匹配）

### 隐藏条件
警告区域在以下情况下隐藏：

1. **配置值不为 "1"**（不使用队列验证）
2. **患者编号不匹配**
3. **队列验证成功**
4. **清空输入时**
5. **程序初始化时**

## 技术实现

### 1. 新增枚举类型
```csharp
public enum QueueVerificationResult
{
    Success,           // 验证成功
    PatientIdMismatch, // 患者编号不匹配
    QueueNotFound,     // 队列中未找到患者
    InfoMismatch       // 队列信息不匹配
}
```

### 2. 修改验证方法
```csharp
private async Task<QueueVerificationResult> VerifyWithQueueInfoAsync(string scannedId)
{
    // 返回详细的验证结果而不是简单的布尔值
    // 可以区分不同的失败原因
}
```

### 3. 警告显示逻辑
```csharp
// 判断是否需要显示队列警告
shouldShowQueueWarning = (queueResult == QueueVerificationResult.QueueNotFound || 
                        queueResult == QueueVerificationResult.InfoMismatch) &&
                        (scannedId == _currentPatientInfo.PatientId);

// 控制警告区域显示
QueueWarningBorder.Visibility = shouldShowQueueWarning ? Visibility.Visible : Visibility.Collapsed;
```

## 调试设置

### 当前状态
- 警告区域在XAML中设置为 `Visibility="Visible"`
- 这样可以在界面设计时看到效果
- 实际运行时会根据逻辑控制显示/隐藏

### 正式版本设置
正式版本时需要将XAML中的可见性改为：
```xml
Visibility="Collapsed"
```

## 业务逻辑说明

### 使用场景
1. **医院叫号系统集成**：当启用队列验证时，系统会查询叫号系统的队列信息
2. **数据一致性检查**：确保患者信息在不同系统间的一致性
3. **异常情况提醒**：当队列数据异常时及时提醒操作人员

### 验证流程
1. 扫描/输入患者编号
2. 检查编号是否与当前患者匹配
3. 如果匹配，查询叫号系统队列
4. 比较队列中的患者信息（姓名、性别）
5. 根据结果显示相应的提示

### 错误处理
- 网络异常或数据库连接失败时，视为队列未找到
- 显示警告提示，但不阻止核对流程
- 确保系统的容错性和可用性

## 测试建议

### 测试用例
1. **正常情况**：患者编号匹配，队列信息一致
2. **队列未找到**：患者编号匹配，但队列中无此患者
3. **信息不匹配**：患者编号匹配，但姓名或性别不一致
4. **编号不匹配**：输入错误的患者编号
5. **配置关闭**：ConfigValue != "1" 时不显示警告

### 验证要点
- 警告区域的显示/隐藏是否正确
- 样式和布局是否美观
- 文字提示是否清晰易懂
- 不同验证结果的处理是否正确

## 后续优化

### 可能的改进
1. **动态文字**：根据不同的失败原因显示不同的提示文字
2. **颜色区分**：不同严重程度使用不同的颜色
3. **详细信息**：显示更多的调试信息（开发模式）
4. **用户配置**：允许用户配置是否显示此警告

### 扩展功能
1. **日志记录**：记录队列验证的详细日志
2. **统计分析**：统计队列验证的成功率
3. **自动重试**：队列查询失败时自动重试
4. **手动刷新**：提供手动刷新队列信息的按钮
