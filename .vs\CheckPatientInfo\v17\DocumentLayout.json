{"Version": 1, "WorkspaceRootPath": "E:\\vs_work\\CheckPatientInfo\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{2A847972-25E8-429C-8DAE-F7C73861DE3B}|CheckPatientInfo.csproj|E:\\vs_work\\CheckPatientInfo\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{2A847972-25E8-429C-8DAE-F7C73861DE3B}|CheckPatientInfo.csproj|solutionrelative:mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{00000000-0000-0000-0000-000000000000}|<Solution>|CheckPatientInfo||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|"}, {"AbsoluteMoniker": "D:0:0:{2A847972-25E8-429C-8DAE-F7C73861DE3B}|CheckPatientInfo.csproj|e:\\vs_work\\checkpatientinfo\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A847972-25E8-429C-8DAE-F7C73861DE3B}|CheckPatientInfo.csproj|solutionrelative:mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2A847972-25E8-429C-8DAE-F7C73861DE3B}|CheckPatientInfo.csproj|e:\\vs_work\\checkpatientinfo\\||{B270807C-D8C6-49EB-8EBE-8E8D566637A1}|6185191f-1008-4fb2-a715-3a4e4f27e610"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 3, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Bookmark", "Name": "ST:0:0:{1c64b9c2-e352-428e-a56d-0ace190b99a6}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "CheckPatientInfo", "DocumentMoniker": "E:\\vs_work\\CheckPatientInfo\\CheckPatientInfo.csproj", "RelativeDocumentMoniker": "CheckPatientInfo.csproj", "ToolTip": "E:\\vs_work\\CheckPatientInfo\\CheckPatientInfo.csproj", "RelativeToolTip": "CheckPatientInfo.csproj", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-07-31T02:20:56.297Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "MainWindow.xaml", "DocumentMoniker": "E:\\vs_work\\CheckPatientInfo\\MainWindow.xaml", "RelativeDocumentMoniker": "MainWindow.xaml", "ToolTip": "E:\\vs_work\\CheckPatientInfo\\MainWindow.xaml", "RelativeToolTip": "MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-30T09:17:20.79Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "MainWindow.xaml.cs", "DocumentMoniker": "E:\\vs_work\\CheckPatientInfo\\MainWindow.xaml.cs", "RelativeDocumentMoniker": "MainWindow.xaml.cs", "ToolTip": "E:\\vs_work\\CheckPatientInfo\\MainWindow.xaml.cs", "RelativeToolTip": "MainWindow.xaml.cs", "ViewState": "AgIAAGYBAAAAAAAAAAAuwMABAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-30T09:17:19.616Z", "EditorCaption": ""}]}]}]}