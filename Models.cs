using System;

namespace CheckPatientInfo
{
    /// <summary>
    /// 患者检查信息模型
    /// </summary>
    public class PatientCheckInfo
    {
        /// <summary>
        /// 患者信息类型（门诊CT、门诊DR等）
        /// </summary>
        public string TypeName { get; set; }

        /// <summary>
        /// 患者姓名
        /// </summary>
        public string PatientName { get; set; }

        /// <summary>
        /// 性别和年龄（合并字段）
        /// </summary>
        public string SexAndAge { get; set; }

        /// <summary>
        /// 患者编号
        /// </summary>
        public string PatientId { get; set; }

        /// <summary>
        /// 检查号
        /// </summary>
        public string StudyId { get; set; }

        /// <summary>
        /// 科室和床号（合并字段）
        /// </summary>
        public string DeptAndBed { get; set; }

        /// <summary>
        /// 检查项目
        /// </summary>
        public string StudyScription { get; set; }

        /// <summary>
        /// 入室核对人
        /// </summary>
        public string CheckVerifyId { get; set; }

        /// <summary>
        /// 入室核对时间
        /// </summary>
        public DateTime? CheckVerifyTime { get; set; }

        /// <summary>
        /// 参数设置值
        /// </summary>
        public string ConfigValue { get; set; }

        /// <summary>
        /// 排队号
        /// </summary>
        public string QueueNo { get; set; }

        /// <summary>
        /// 当前登录用户
        /// </summary>
        public string CurrentUser { get; set; }

        // 保留原有字段用于兼容性（队列验证时使用）
        /// <summary>
        /// 性别（用于队列验证）
        /// </summary>
        public string Sex { get; set; }

        /// <summary>
        /// 年龄（用于队列验证）
        /// </summary>
        public string Age { get; set; }

        /// <summary>
        /// 开单科室（用于队列验证）
        /// </summary>
        public string DepartmentName { get; set; }
    }

    /// <summary>
    /// 队列信息模型
    /// </summary>
    public class QueueInfo
    {
        /// <summary>
        /// 患者姓名
        /// </summary>
        public string PatientName { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public string Sex { get; set; }

        /// <summary>
        /// 检查号
        /// </summary>
        public string StudyId { get; set; }
    }
}
