# 配置文件功能说明

## 功能概述

移除了数据库中的configvalue查询，改为使用本地配置文件`ConvergePACS.ini`来控制核对和自动关闭功能。

## 配置文件格式

### 文件位置
`e:\vs_work\CheckPatientInfo\ConvergePACS.ini`

### 配置内容
```ini
[checkverify]
#核对时是否判断是否当前呼叫患者（1 启用 0：不启用）
linkqueue=1
#核对成功是否自动关闭（1 启用 0：不启用）
autoclose=1
#自动关闭倒计时时间（最大5秒，0则立即关闭。autoclose=1时生效）
closesec=5
```

## 配置项说明

### 1. linkqueue（队列验证）
- **功能**：控制是否启用队列验证功能
- **取值**：
  - `1`：启用队列验证，会查询叫号系统队列信息
  - `0`：不启用队列验证，直接比较患者编号
- **默认值**：读取失败时默认为`0`

### 2. autoclose（自动关闭）
- **功能**：控制核对成功后是否自动关闭程序
- **取值**：
  - `1`：启用自动关闭功能
  - `0`：不启用自动关闭功能，需要手动关闭
- **默认值**：读取失败时默认为`0`

### 3. closesec（关闭秒数）
- **功能**：设置自动关闭的倒计时时间
- **取值**：`0-5`秒
  - `0`：立即关闭，不显示倒计时
  - `1-5`：显示倒计时后关闭
- **默认值**：读取失败或超出范围时默认为`0`
- **生效条件**：只有当`autoclose=1`时才生效

## 代码实现

### 1. ConfigHelper类扩展

#### 新增方法
```csharp
// 获取队列验证配置
public static bool GetLinkQueueConfig()

// 获取自动关闭配置  
public static bool GetAutoCloseConfig()

// 获取关闭秒数配置
public static int GetCloseSecondsConfig()

// 私有方法：读取配置文件
private static string GetConvergeConfig(string key, string defaultValue)
```

#### 配置读取逻辑
- 支持注释行（以#开头）
- 忽略空行
- 区分大小写不敏感
- 读取失败时返回默认值

### 2. 数据模型简化

#### 移除字段
```csharp
// 移除了ConfigValue字段
// public string ConfigValue { get; set; }
```

#### 数据库查询简化
```sql
-- 移除了configvalue字段的查询
select t.typename, t.patientname, t.SexAndAge, t.sex, t.patientid, 
       t.studyid, t.DeptandBed, t.studyscription, t.checkverifyid, 
       t.checkverifytime, t.queueno
from v_checkverify_view t
where t.checkserialnum = '传入的检查流水号'
```

### 3. 核对逻辑更新

#### 队列验证判断
```csharp
// 原来：if (_currentPatientInfo.ConfigValue == "1")
// 现在：if (ConfigHelper.GetLinkQueueConfig())
```

#### 自动关闭判断
```csharp
// 原来：固定启动倒计时
// 现在：根据配置决定
if (ConfigHelper.GetAutoCloseConfig())
{
    StartCountdownTimer();
}
```

### 4. 倒计时逻辑优化

#### 动态秒数
```csharp
// 从配置文件读取倒计时秒数
_countdownSeconds = ConfigHelper.GetCloseSecondsConfig();

// 如果配置为0秒，立即关闭
if (_countdownSeconds == 0)
{
    Application.Current.Shutdown(0);
    return;
}
```

#### 标题显示逻辑
```csharp
// 如果配置的关闭秒数为0，不显示倒计时
if (ConfigHelper.GetCloseSecondsConfig() == 0)
{
    return; // 保持原标题不变
}

this.Title = $"影像系统入室核对-{_countdownSeconds}秒后自动关闭";
```

## 使用场景

### 场景1：启用队列验证 + 自动关闭
```ini
linkqueue=1
autoclose=1
closesec=5
```
- 核对时会验证队列信息
- 核对成功后5秒倒计时自动关闭

### 场景2：不启用队列验证 + 手动关闭
```ini
linkqueue=0
autoclose=0
closesec=0
```
- 核对时只比较患者编号
- 核对成功后不自动关闭，需要手动关闭

### 场景3：启用队列验证 + 立即关闭
```ini
linkqueue=1
autoclose=1
closesec=0
```
- 核对时会验证队列信息
- 核对成功后立即关闭，不显示倒计时

### 场景4：启用队列验证 + 3秒后关闭
```ini
linkqueue=1
autoclose=1
closesec=3
```
- 核对时会验证队列信息
- 核对成功后3秒倒计时自动关闭

## 错误处理

### 配置文件不存在
- 所有配置项使用默认值
- 程序正常运行，不会报错

### 配置项格式错误
- 使用默认值替代
- 程序继续正常运行

### 配置值超出范围
- `closesec`超出0-5范围时，使用默认值0
- 其他配置项使用默认值

## 部署注意事项

1. **配置文件位置**：确保`ConvergePACS.ini`文件在程序根目录
2. **配置文件格式**：确保使用正确的ini文件格式
3. **权限设置**：确保程序有读取配置文件的权限
4. **配置验证**：部署后验证各配置项是否按预期工作

## 优势

1. **灵活配置**：无需修改代码即可调整功能
2. **本地控制**：不依赖数据库配置，提高可靠性
3. **易于维护**：配置文件格式简单，易于理解和修改
4. **向后兼容**：配置读取失败时使用安全的默认值

功能已完整实现，提供了灵活的本地配置控制！
