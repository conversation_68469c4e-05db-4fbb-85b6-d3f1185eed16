# 字段合并和新增功能说明

## 修改概述

根据用户需求，对左侧患者信息区域进行了字段合并和新增，使用新的视图查询和分离的用户查询。

## 界面变更

### 1. 字段合并
- **性别、年龄合并**：原来分别显示"性别"和"年龄"，现在合并为"性别/年龄"
- **科室和床号合并**：原来只显示"开单科室"，现在合并为"科室/床号"

### 2. 新增字段
- **排队号**：新增显示排队号信息，字段名为 `queueno`

### 3. 界面布局
```
姓名: 张三
性别/年龄: 男 / 45岁
患者编号: P202401001
科室/床号: 骨二病区二区护理单元 / 65床
排队号: A065
检查项目: [检查项目详情]
```

## 数据库变更

### 1. 新的查询SQL
```sql
-- 患者信息查询（使用视图）
select t.typename,          -- 患者信息旁边的括号的内容，值一般是门诊CT、门诊DR等
       t.patientname,       -- 患者姓名
       t.SexAndAge,         -- 性别和年龄（合并字段）
       t.sex,               -- 性别（用于队列验证）
       t.patientid,         -- 患者编号
       t.studyid,           -- 检查号
       t.DeptandBed,        -- 科室和床号（合并字段）
       t.studyscription,    -- 检查项目
       t.checkverifyid,     -- 核对人姓名
       t.checkverifytime,   -- 核对时间
       t.configvalue,       -- 参数设置
       t.queueno            -- 排队号（新增）
  from v_checkverify_view t
 where t.checkserialnum = '传入的检查流水号'
```

### 2. 用户查询SQL
```sql
-- 当前登录用户查询（分离查询）
select username from pacsuser where userid='传入的userid'
```

## 代码实现

### 1. 数据模型更新 (Models.cs)

#### 主要字段
```csharp
/// <summary>
/// 性别和年龄（合并字段）
/// </summary>
public string SexAndAge { get; set; }

/// <summary>
/// 性别（用于队列验证）
/// </summary>
public string Sex { get; set; }

/// <summary>
/// 科室和床号（合并字段）
/// </summary>
public string DeptAndBed { get; set; }

/// <summary>
/// 排队号
/// </summary>
public string QueueNo { get; set; }
```

### 2. 数据服务更新 (PatientDataService.cs)

#### 并行查询
```csharp
// 并行执行患者信息查询和用户查询
var patientTask = ExecuteQueryAsync(patientSql);
var userTask = ExecuteQueryAsync(userSql);
await Task.WhenAll(patientTask, userTask);
```

#### 字段解析
```csharp
case "SEXANDAGE":
    info.SexAndAge = value;
    break;
case "SEX":
    info.Sex = value;  // 用于队列验证
    break;
case "DEPTANDBED":
    info.DeptAndBed = value;
    break;
case "QUEUENO":
    info.QueueNo = value;
    break;
```

### 3. 界面更新 (MainWindow.xaml.cs)

```csharp
PatientNameText.Text = _currentPatientInfo.PatientName ?? "";
PatientGenderText.Text = _currentPatientInfo.SexAndAge ?? "";      // 合并字段
PatientIdText.Text = _currentPatientInfo.PatientId ?? "";
OrderDepartmentText.Text = _currentPatientInfo.DeptAndBed ?? "";   // 合并字段
QueueText.Text = _currentPatientInfo.QueueNo ?? "";                // 新增字段
ExamTypeText.Text = _currentPatientInfo.StudyScription ?? "";
```

## 队列验证

### 队列验证逻辑
- 队列验证只需要比较**患者姓名**和**性别**
- 使用单独的 `Sex` 字段进行性别比较
- 不需要解析合并字段，直接使用视图返回的 `t.sex` 字段

### 验证代码
```csharp
// 比较姓名和性别
bool infoMatches = queueInfo.PatientName == _currentPatientInfo.PatientName &&
                  queueInfo.Sex == _currentPatientInfo.Sex;
```

## 性能优化

### 1. 并行查询
- 患者信息查询和用户查询并行执行
- 减少总的查询时间

### 2. 视图使用
- 使用预定义视图 `v_checkverify_view`
- 简化查询逻辑，提高查询效率

## 测试要点

### 1. 界面显示
- 验证性别/年龄合并显示正确
- 验证科室/床号合并显示正确
- 验证排队号显示正确

### 2. 队列验证
- 确认队列验证功能仍然正常工作
- 验证性别比较逻辑正确（只比较姓名和性别）

### 3. 错误处理
- 测试查询失败的处理
- 测试字段为空的处理

## 部署注意事项

1. **数据库视图**：确保 `v_checkverify_view` 视图已创建并包含所需字段：
   - `SexAndAge`（合并显示用）
   - `sex`（队列验证用）
   - `DeptandBed`（合并显示用）
   - `queueno`（新增显示用）

2. **字段映射**：确认视图中的字段名与代码中的解析逻辑一致

功能已完整实现，代码简洁清晰，队列验证逻辑保持不变！
