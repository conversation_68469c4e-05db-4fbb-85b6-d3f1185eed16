using System;
using System.IO;

namespace CheckPatientInfo
{
    /// <summary>
    /// 配置文件帮助类
    /// </summary>
    public static class ConfigHelper
    {
        /// <summary>
        /// 获取数据库连接URL
        /// </summary>
        /// <returns>数据库连接URL</returns>
        public static string GetConnectionUrl()
        {
            try
            {
                string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Connection.ini");

                if (!File.Exists(configPath))
                {
                    throw new FileNotFoundException($"配置文件不存在：{configPath}");
                }

                string[] lines = File.ReadAllLines(configPath);
                bool inConnectionSection = false;

                foreach (string line in lines)
                {
                    string trimmedLine = line.Trim();

                    // 检查是否进入[Connection]节
                    if (trimmedLine.Equals("[Connection]", StringComparison.OrdinalIgnoreCase))
                    {
                        inConnectionSection = true;
                        continue;
                    }

                    // 如果遇到其他节，退出Connection节
                    if (trimmedLine.StartsWith("[") && trimmedLine.EndsWith("]") &&
                        !trimmedLine.Equals("[Connection]", StringComparison.OrdinalIgnoreCase))
                    {
                        inConnectionSection = false;
                        continue;
                    }

                    // 在Connection节中查找Url配置
                    if (inConnectionSection && trimmedLine.StartsWith("Url=", StringComparison.OrdinalIgnoreCase))
                    {
                        return trimmedLine.Substring(4).Trim();
                    }
                }

                throw new InvalidOperationException("在Connection.ini文件中未找到[Connection]节下的Url配置");
            }
            catch (Exception ex)
            {
                throw new Exception($"读取配置文件失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取队列验证配置（是否启用队列验证）
        /// </summary>
        /// <returns>true表示启用，false表示不启用</returns>
        public static bool GetLinkQueueConfig()
        {
            return GetConvergeConfig("linkqueue", "0") == "1";
        }

        /// <summary>
        /// 获取自动关闭配置（是否启用自动关闭）
        /// </summary>
        /// <returns>true表示启用，false表示不启用</returns>
        public static bool GetAutoCloseConfig()
        {
            return GetConvergeConfig("autoclose", "0") == "1";
        }

        /// <summary>
        /// 获取自动关闭倒计时秒数
        /// </summary>
        /// <returns>倒计时秒数（0-5秒）</returns>
        public static int GetCloseSecondsConfig()
        {
            string value = GetConvergeConfig("closesec", "0");
            if (int.TryParse(value, out int seconds))
            {
                // 限制在0-5秒范围内
                if (seconds >= 0 && seconds <= 5)
                {
                    return seconds;
                }
            }
            return 0; // 默认值或无效值时返回0
        }

        /// <summary>
        /// 从ConvergePACS.ini文件读取checkverify节的配置
        /// </summary>
        /// <param name="key">配置键名</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        private static string GetConvergeConfig(string key, string defaultValue)
        {
            try
            {
                string configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ConvergePACS.ini");

                if (!File.Exists(configPath))
                {
                    return defaultValue;
                }

                string[] lines = File.ReadAllLines(configPath);
                bool inCheckVerifySection = false;

                foreach (string line in lines)
                {
                    string trimmedLine = line.Trim();

                    // 跳过注释行
                    if (trimmedLine.StartsWith("#") || string.IsNullOrEmpty(trimmedLine))
                    {
                        continue;
                    }

                    // 检查是否进入[checkverify]节
                    if (trimmedLine.Equals("[checkverify]", StringComparison.OrdinalIgnoreCase))
                    {
                        inCheckVerifySection = true;
                        continue;
                    }

                    // 如果遇到其他节，退出checkverify节
                    if (trimmedLine.StartsWith("[") && trimmedLine.EndsWith("]") &&
                        !trimmedLine.Equals("[checkverify]", StringComparison.OrdinalIgnoreCase))
                    {
                        inCheckVerifySection = false;
                        continue;
                    }

                    // 在checkverify节中查找指定的配置
                    if (inCheckVerifySection && trimmedLine.StartsWith($"{key}=", StringComparison.OrdinalIgnoreCase))
                    {
                        return trimmedLine.Substring(key.Length + 1).Trim();
                    }
                }

                return defaultValue;
            }
            catch
            {
                return defaultValue;
            }
        }
    }
}
