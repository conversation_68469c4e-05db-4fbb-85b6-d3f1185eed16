using System;
using System.Net.Http;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Xml;

namespace CheckPatientInfo
{
    /// <summary>
    /// 患者数据服务类
    /// </summary>
    public class PatientDataService : IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly string _connectionUrl;

        public PatientDataService()
        {
            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromSeconds(30);
            _connectionUrl = ConfigHelper.GetConnectionUrl();
        }

        /// <summary>
        /// 获取患者检查信息
        /// </summary>
        /// <param name="checkSerialNum">检查流水号</param>
        /// <param name="userId">用户ID</param>
        /// <returns>患者检查信息</returns>
        public async Task<PatientCheckInfo> GetPatientCheckInfoAsync(string checkSerialNum, string userId)
        {
            // 使用新的视图查询患者信息
            string patientSql = $@"
                select t.typename,
                       t.patientname,
                       t.SexAnd<PERSON>ge,
                       t.patientid,
                       t.studyid,
                       t.DeptandBed,
                       t.studyscription,
                       t.checkverifyid,
                       t.checkverifytime,
                       t.configvalue,
                       t.queueno
                  from v_checkverify_view t
                 where t.checkserialnum = '{checkSerialNum}'";

            // 单独查询当前登录用户
            string userSql = $@"
                select username from pacsuser where userid='{userId}'";

            try
            {
                // 并行执行两个查询
                var patientTask = ExecuteQueryAsync(patientSql);
                var userTask = ExecuteQueryAsync(userSql);

                await Task.WhenAll(patientTask, userTask);

                string patientResponse = await patientTask;
                string userResponse = await userTask;

                var patientInfo = ParsePatientCheckInfo(patientResponse);
                if (patientInfo != null)
                {
                    // 设置当前用户
                    patientInfo.CurrentUser = ParseCurrentUser(userResponse);
                }

                return patientInfo;
            }
            catch (Exception ex)
            {
                throw new Exception($"查询患者信息失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取队列信息
        /// </summary>
        /// <param name="studyId">检查号</param>
        /// <returns>队列信息</returns>
        public async Task<QueueInfo> GetQueueInfoAsync(string studyId)
        {
            string sql = $@"
                select t.patientname,t.sex,t.studyid from V_CURRENT_QUEUEINFO t where t.studyid='{studyId}'";

            string response = await ExecuteQueryAsync(sql);
            return ParseQueueInfo(response);
        }

        /// <summary>
        /// 更新核对信息
        /// </summary>
        /// <param name="checkSerialNum">检查流水号</param>
        /// <param name="userId">用户ID</param>
        /// <returns>是否更新成功</returns>
        public async Task<bool> UpdateVerificationAsync(string checkSerialNum, string userId)
        {
            string sql = $@"
                update studyinfo 
                set checkverifyid='{userId}', checkverifytime=sysdate 
                where checkserialnum='{checkSerialNum}'";

            try
            {
                string response = await ExecuteUpdateAsync(sql);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private async Task<string> ExecuteQueryAsync(string sql)
        {
            try
            {
                string xmlRequest = CreateQueryXml(sql);
                var content = new StringContent(xmlRequest, Encoding.UTF8, "application/xml");

                HttpResponseMessage response = await _httpClient.PostAsync(_connectionUrl, content);

                byte[] responseBytes = await response.Content.ReadAsByteArrayAsync();
                string responseContent = ConvertGbkToUtf8(responseBytes);

                if (IsErrorResponse(responseContent))
                {
                    throw new Exception($"服务器返回错误：{ExtractErrorMessage(responseContent)}");
                }

                return responseContent;
            }
            catch (Exception ex)
            {
                throw new Exception($"数据库查询失败: {ex.Message}");
            }
        }

        private async Task<string> ExecuteUpdateAsync(string sql)
        {
            try
            {
                string xmlRequest = CreateUpdateXml(sql);
                var content = new StringContent(xmlRequest, Encoding.UTF8, "application/xml");
                
                HttpResponseMessage response = await _httpClient.PostAsync(_connectionUrl, content);
                
                byte[] responseBytes = await response.Content.ReadAsByteArrayAsync();
                return ConvertGbkToUtf8(responseBytes);
            }
            catch (Exception ex)
            {
                throw new Exception($"数据库更新失败: {ex.Message}");
            }
        }

        private string CreateQueryXml(string sql)
        {
            return $@"<request>
    <sqltype>query</sqltype>
    <offset>0</offset>
    <limit>10</limit>
    <statements>
        <statement name=""query1"">
            <sql><![CDATA[{sql}]]></sql>
            <input>
            </input>
            <out>
            </out>
        </statement>
    </statements>
</request>";
        }

        private string CreateUpdateXml(string sql)
        {
            return $@"<request>
    <sqltype>update</sqltype>
    <offset>0</offset>
    <limit>10</limit>
    <statements>
        <statement name=""update1"">
            <sql><![CDATA[{sql}]]></sql>
            <input>
            </input>
            <out>
            </out>
        </statement>
    </statements>
</request>";
        }

        private string ConvertGbkToUtf8(byte[] gbkBytes)
        {
            try
            {
                Encoding gbkEncoding = Encoding.GetEncoding("GBK");
                return gbkEncoding.GetString(gbkBytes);
            }
            catch
            {
                return Encoding.UTF8.GetString(gbkBytes);
            }
        }

        private bool IsErrorResponse(string responseContent)
        {
            if (string.IsNullOrEmpty(responseContent))
                return false;

            string content = responseContent.ToLower();
            return content.Contains("<html") || 
                   content.Contains("http status") || 
                   content.Contains("error") && content.Contains("<title>");
        }

        private string ExtractErrorMessage(string errorResponse)
        {
            try
            {
                var titleMatch = Regex.Match(errorResponse, @"<title[^>]*>([^<]+)</title>", RegexOptions.IgnoreCase);
                if (titleMatch.Success)
                {
                    return titleMatch.Groups[1].Value.Trim();
                }
                return "服务器返回错误";
            }
            catch
            {
                return "解析错误信息失败";
            }
        }

        /// <summary>
        /// 解析当前用户信息
        /// </summary>
        /// <param name="xmlResponse">XML响应</param>
        /// <returns>用户名</returns>
        private string ParseCurrentUser(string xmlResponse)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(xmlResponse))
                {
                    return "未知用户";
                }

                XmlDocument doc = new XmlDocument();
                doc.LoadXml(xmlResponse);

                XmlNamespaceManager nsmgr = new XmlNamespaceManager(doc.NameTable);
                nsmgr.AddNamespace("rs", "urn:schemas-microsoft-com:rowset");
                nsmgr.AddNamespace("z", "#RowsetSchema");

                XmlNode rowNode = doc.SelectSingleNode("//rs:data/z:row", nsmgr);
                if (rowNode?.Attributes?["USERNAME"] != null)
                {
                    return rowNode.Attributes["USERNAME"].Value?.Trim() ?? "未知用户";
                }

                return "未知用户";
            }
            catch
            {
                return "未知用户";
            }
        }

        private PatientCheckInfo ParsePatientCheckInfo(string xmlResponse)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(xmlResponse))
                {
                    return null;
                }

                XmlDocument doc = new XmlDocument();
                doc.LoadXml(xmlResponse);

                // 根据接口文档，查找 rs:data/z:row 节点
                XmlNamespaceManager nsmgr = new XmlNamespaceManager(doc.NameTable);
                nsmgr.AddNamespace("rs", "urn:schemas-microsoft-com:rowset");
                nsmgr.AddNamespace("z", "#RowsetSchema");

                XmlNode rowNode = doc.SelectSingleNode("//rs:data/z:row", nsmgr);
                if (rowNode == null)
                {
                    return null;
                }

                var info = new PatientCheckInfo();

                // 根据接口文档，字段作为属性存储在z:row节点中
                if (rowNode.Attributes != null)
                {
                    foreach (XmlAttribute attr in rowNode.Attributes)
                    {
                        string value = attr.Value?.Trim();
                        string fieldName = attr.Name.ToUpper(); // 属性名通常是大写

                        switch (fieldName)
                        {
                            case "TYPENAME":
                                info.TypeName = value;
                                break;
                            case "PATIENTNAME":
                                info.PatientName = value;
                                break;
                            case "SEXANDAGE":
                                info.SexAndAge = value;
                                // 尝试解析性别和年龄用于兼容性
                                ParseSexAndAge(value, info);
                                break;
                            case "PATIENTID":
                                info.PatientId = value;
                                break;
                            case "STUDYID":
                                info.StudyId = value;
                                break;
                            case "DEPTANDBED":
                                info.DeptAndBed = value;
                                // 尝试解析科室名用于兼容性
                                ParseDeptAndBed(value, info);
                                break;
                            case "STUDYSCRIPTION":
                                info.StudyScription = value;
                                break;
                            case "CHECKVERIFYID":
                                info.CheckVerifyId = value;
                                break;
                            case "CHECKVERIFYTIME":
                                if (!string.IsNullOrEmpty(value) && DateTime.TryParse(value, out DateTime verifyTime))
                                {
                                    info.CheckVerifyTime = verifyTime;
                                }
                                break;
                            case "CONFIGVALUE":
                                info.ConfigValue = value;
                                break;
                            case "QUEUENO":
                                info.QueueNo = value;
                                break;
                            // 保留原有字段的解析用于兼容性
                            case "SEX":
                                info.Sex = value;
                                break;
                            case "AGE":
                                info.Age = value;
                                break;
                            case "DEPARTMENTNAME":
                                info.DepartmentName = value;
                                break;
                        }
                    }
                }

                return info;
            }
            catch (Exception ex)
            {
                throw new Exception($"解析患者信息失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 解析性别和年龄字段
        /// </summary>
        /// <param name="sexAndAge">性别和年龄合并字段</param>
        /// <param name="info">患者信息对象</param>
        private void ParseSexAndAge(string sexAndAge, PatientCheckInfo info)
        {
            if (string.IsNullOrEmpty(sexAndAge))
                return;

            try
            {
                // 尝试解析格式如："男 / 45岁" 或 "男/45岁" 或 "男 45岁"
                var parts = sexAndAge.Split(new char[] { '/', '/', ' ' }, StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length >= 2)
                {
                    info.Sex = parts[0].Trim();
                    info.Age = parts[1].Trim();
                }
                else if (parts.Length == 1)
                {
                    // 如果只有一个部分，尝试从中提取性别和年龄
                    string text = parts[0].Trim();
                    if (text.Contains("男"))
                    {
                        info.Sex = "男";
                        info.Age = text.Replace("男", "").Trim();
                    }
                    else if (text.Contains("女"))
                    {
                        info.Sex = "女";
                        info.Age = text.Replace("女", "").Trim();
                    }
                }
            }
            catch
            {
                // 解析失败时保持原值
            }
        }

        /// <summary>
        /// 解析科室和床号字段
        /// </summary>
        /// <param name="deptAndBed">科室和床号合并字段</param>
        /// <param name="info">患者信息对象</param>
        private void ParseDeptAndBed(string deptAndBed, PatientCheckInfo info)
        {
            if (string.IsNullOrEmpty(deptAndBed))
                return;

            try
            {
                // 尝试解析格式如："骨二病区二区护理单元 / 65床"
                var parts = deptAndBed.Split(new char[] { '/', '/' }, StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length >= 1)
                {
                    info.DepartmentName = parts[0].Trim();
                }
            }
            catch
            {
                // 解析失败时保持原值
            }
        }

        private QueueInfo ParseQueueInfo(string xmlResponse)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(xmlResponse))
                {
                    return null;
                }

                XmlDocument doc = new XmlDocument();
                doc.LoadXml(xmlResponse);

                // 使用Microsoft Rowset格式解析
                XmlNamespaceManager nsmgr = new XmlNamespaceManager(doc.NameTable);
                nsmgr.AddNamespace("rs", "urn:schemas-microsoft-com:rowset");
                nsmgr.AddNamespace("z", "#RowsetSchema");

                XmlNode rowNode = doc.SelectSingleNode("//rs:data/z:row", nsmgr);
                if (rowNode == null)
                {
                    return null;
                }

                var info = new QueueInfo();

                if (rowNode.Attributes != null)
                {
                    foreach (XmlAttribute attr in rowNode.Attributes)
                    {
                        string value = attr.Value?.Trim();
                        string fieldName = attr.Name.ToUpper();

                        switch (fieldName)
                        {
                            case "PATIENTNAME":
                                info.PatientName = value;
                                break;
                            case "SEX":
                                info.Sex = value;
                                break;
                            case "STUDYID":
                                info.StudyId = value;
                                break;
                        }
                    }
                }

                return info;
            }
            catch (Exception ex)
            {
                throw new Exception($"解析队列信息失败：{ex.Message}");
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
