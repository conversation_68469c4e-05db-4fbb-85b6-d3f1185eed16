# 倒计时自动关闭功能说明

## 功能概述

实现了核对成功后的倒计时自动关闭功能，并根据不同的核对状态设置相应的退出码。

## 功能特性

### 1. 倒计时显示
核对成功后，程序标题会显示倒计时：
- `影像系统入室核对-5秒后自动关闭`
- `影像系统入室核对-4秒后自动关闭`
- `影像系统入室核对-3秒后自动关闭`
- `影像系统入室核对-2秒后自动关闭`
- `影像系统入室核对-1秒后自动关闭`
- 倒计时结束后自动关闭程序

### 2. 退出码设置
根据不同的核对状态设置退出码：
- **退出码 0**：核对成功（自动关闭或手动关闭）
- **退出码 1**：核对失败
- **退出码 2**：没有核对（既没成功也没失败）

## 触发条件

### 倒计时启动条件
倒计时在以下情况下启动：
1. **新核对成功**：当前核对操作成功并保存到数据库
2. **已核对状态**：程序启动时发现该患者已经核对过

### 状态标记
- `_verificationSuccess = true`：核对成功
- `_verificationFailed = true`：核对失败
- 两者都为 false：没有核对

## 技术实现

### 1. 新增字段
```csharp
private bool _verificationSuccess = false;
private bool _verificationFailed = false;
private DispatcherTimer _countdownTimer;
private int _countdownSeconds = 5;
```

### 2. 倒计时方法
```csharp
private void StartCountdownTimer()
{
    _countdownTimer = new DispatcherTimer();
    _countdownTimer.Interval = TimeSpan.FromSeconds(1);
    _countdownTimer.Tick += CountdownTimer_Tick;
    _countdownTimer.Start();
    UpdateCountdownTitle();
}

private void CountdownTimer_Tick(object sender, EventArgs e)
{
    _countdownSeconds--;
    if (_countdownSeconds <= 0)
    {
        _countdownTimer.Stop();
        Application.Current.Shutdown(0); // 退出码0
    }
    else
    {
        UpdateCountdownTitle();
    }
}

private void UpdateCountdownTitle()
{
    this.Title = $"影像系统入室核对-{_countdownSeconds}秒后自动关闭";
}
```

### 3. 退出码处理
```csharp
protected override void OnClosing(System.ComponentModel.CancelEventArgs e)
{
    if (_verificationSuccess)
    {
        Application.Current.Shutdown(0); // 核对成功
    }
    else if (_verificationFailed)
    {
        Application.Current.Shutdown(1); // 核对失败
    }
    else
    {
        Application.Current.Shutdown(2); // 没有核对
    }
    base.OnClosing(e);
}
```

## 使用场景

### 场景1：正常核对成功
1. 用户扫描/输入正确的患者编号
2. 核对成功，显示成功信息
3. 开始5秒倒计时，标题显示倒计时
4. 5秒后自动关闭，退出码为0

### 场景2：已核对患者
1. 程序启动时发现患者已核对过
2. 显示"已核对，无需二次核对"
3. 开始5秒倒计时，标题显示倒计时
4. 5秒后自动关闭，退出码为0

### 场景3：核对失败
1. 用户输入错误的患者编号
2. 显示核对失败信息
3. 用户手动关闭程序，退出码为1

### 场景4：未进行核对
1. 用户打开程序但没有进行核对操作
2. 直接关闭程序，退出码为2

## 用户体验

### 视觉反馈
- 核对成功后立即在标题栏显示倒计时
- 每秒更新倒计时数字
- 给用户明确的自动关闭提示

### 操作选择
- 用户可以等待自动关闭（5秒）
- 用户也可以手动点击关闭按钮
- 两种方式的退出码都是0（核对成功情况下）

## 调用方示例

外部程序可以通过退出码判断核对结果：

```batch
CheckPatientInfo.exe 用户ID#检查流水号
if %ERRORLEVEL% == 0 (
    echo 核对成功
) else if %ERRORLEVEL% == 1 (
    echo 核对失败
) else if %ERRORLEVEL% == 2 (
    echo 未进行核对
)
```

## 注意事项

1. **倒计时只在核对成功时启动**，失败时不会自动关闭
2. **退出码设置在窗口关闭时**，确保所有关闭方式都有正确的退出码
3. **计时器资源清理**，在窗口关闭时停止计时器
4. **已核对状态也会启动倒计时**，保持一致的用户体验

功能已完整实现，提供了良好的用户体验和明确的程序状态反馈！
