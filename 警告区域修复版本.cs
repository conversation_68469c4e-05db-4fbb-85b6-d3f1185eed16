using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;
using System.IO;
using System.Threading.Tasks;
using System.Xml;

namespace CheckPatientInfo
{
    /// <summary>
    /// 队列验证结果枚举
    /// </summary>
    public enum QueueVerificationResult
    {
        Success,           // 验证成功
        PatientIdMismatch, // 患者编号不匹配
        QueueNotFound,     // 队列中未找到患者
        InfoMismatch       // 队列信息不匹配
    }

    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        // 其他代码保持不变...

        /// <summary>
        /// 初始化核对状态显示
        /// </summary>
        private void InitializeVerificationStatus()
        {
            // 初始时不显示警告区域
            QueueWarningBorder.Visibility = Visibility.Collapsed;
            UpdateVerificationStatus();
        }

        /// <summary>
        /// 患者核对功能
        /// </summary>
        private async void VerifyPatient()
        {
            if (_currentPatientInfo == null)
            {
                MessageBox.Show("患者信息未加载完成，请稍后再试！", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            string scannedId = ScanInputTextBox.Text.Trim();
            string expectedId = _currentPatientInfo.PatientId;

            if (string.IsNullOrEmpty(scannedId))
            {
                MessageBox.Show("请输入或扫描患者编号！", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                ScanInputTextBox.Focus();
                return;
            }

            try
            {
                bool verificationResult = false;
                bool shouldShowQueueWarning = false;

                // 根据配置值决定核对方式
                if (_currentPatientInfo.ConfigValue == "1")
                {
                    // 配置值为1，需要查询队列信息进行核对
                    QueueVerificationResult queueResult = await VerifyWithQueueInfoAsync(scannedId);
                    verificationResult = (queueResult == QueueVerificationResult.Success);
                    
                    // 判断是否需要显示队列警告
                    shouldShowQueueWarning = (queueResult == QueueVerificationResult.QueueNotFound || 
                                            queueResult == QueueVerificationResult.InfoMismatch) &&
                                            (scannedId == _currentPatientInfo.PatientId);
                }
                else
                {
                    // 配置值不为1，直接比较患者编号
                    verificationResult = (scannedId == expectedId);
                    shouldShowQueueWarning = false; // 不使用队列验证时不显示警告
                }

                // 显示结果区域
                VerificationResultBorder.Visibility = Visibility.Visible;
                
                // 控制队列警告区域的显示
                QueueWarningBorder.Visibility = shouldShowQueueWarning ? Visibility.Visible : Visibility.Collapsed;

                if (verificationResult)
                {
                    // 核对成功，更新数据库
                    bool updateSuccess = await _dataService.UpdateVerificationAsync(_checkSerialNum, _currentUserId);

                    if (updateSuccess)
                    {
                        ShowVerificationSuccess();

                        // 更新核对状态
                        _isVerified = true;
                        _verifierName = _currentPatientInfo.CurrentUser ?? "未知用户";
                        _verificationTime = DateTime.Now;
                        UpdateVerificationStatus();

                        // 禁用输入和按钮
                        ScanInputTextBox.IsEnabled = false;
                        VerifyButton.IsEnabled = false;
                        ClearButton.IsEnabled = false;
                    }
                    else
                    {
                        ShowVerificationError("核对成功但保存失败，请联系管理员！");
                    }
                }
                else
                {
                    // 核对失败
                    ShowVerificationError($"核对失败！患者号{scannedId}与当前患者不匹配！");
                }
            }
            catch (Exception ex)
            {
                ShowVerificationError($"核对过程中发生错误,请联系管理员！");//{ex.Message}
            }
        }

        /// <summary>
        /// 使用队列信息进行核对
        /// </summary>
        /// <param name="scannedId">扫描的患者编号</param>
        /// <returns>核对结果枚举</returns>
        private async Task<QueueVerificationResult> VerifyWithQueueInfoAsync(string scannedId)
        {
            try
            {
                // 首先检查扫描的编号是否与患者编号一致
                if (scannedId != _currentPatientInfo.PatientId)
                {
                    return QueueVerificationResult.PatientIdMismatch;
                }

                // 查询队列信息
                QueueInfo queueInfo = await _dataService.GetQueueInfoAsync(_currentPatientInfo.StudyId);

                if (queueInfo == null)
                {
                    // 队列中未找到患者
                    return QueueVerificationResult.QueueNotFound;
                }

                // 比较姓名和性别
                bool infoMatches = queueInfo.PatientName == _currentPatientInfo.PatientName &&
                                  queueInfo.Sex == _currentPatientInfo.Sex;
                
                return infoMatches ? QueueVerificationResult.Success : QueueVerificationResult.InfoMismatch;
            }
            catch
            {
                // 发生异常时，视为队列未找到
                return QueueVerificationResult.QueueNotFound;
            }
        }

        /// <summary>
        /// 清空输入
        /// </summary>
        private void ClearInput()
        {
            // 如果已经核对过，不允许清空
            if (_isVerified)
            {
                return;
            }

            ScanInputTextBox.Text = "";
            VerificationResultBorder.Visibility = Visibility.Collapsed;
            QueueWarningBorder.Visibility = Visibility.Collapsed;

            ScanInputTextBox.Focus();
        }

        // 其他代码保持不变...
    }
}
